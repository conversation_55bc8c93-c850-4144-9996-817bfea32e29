Flutter crash report.
Please report a bug at https://github.com/flutter/flutter/issues.

## command

flutter test

## exception

ShaderCompilerException: ShaderCompilerException: Shader compilation of "C:\FlutterDev\flutter\packages\flutter\lib\src\material\shaders\ink_sparkle.frag" to "build\unit_test_assets\shaders/ink_sparkle.frag" failed with exit code 1.
impellerc stdout:

impellerc stderr:
Could not write file to build\unit_test_assets\shaders/ink_sparkle.frag




```
#0      ShaderCompiler.compileShader (package:flutter_tools/src/build_system/tools/shader_compiler.dart:194:9)
<asynchronous suspension>
#1      writeBundle.<anonymous closure> (package:flutter_tools/src/bundle_builder.dart:215:20)
<asynchronous suspension>
#2      Future.wait.<anonymous closure> (dart:async/future.dart:525:21)
<asynchronous suspension>
#3      writeBundle (package:flutter_tools/src/bundle_builder.dart:177:3)
<asynchronous suspension>
#4      TestCommand._buildTestAsset (package:flutter_tools/src/commands/test.dart:779:7)
<asynchronous suspension>
#5      TestCommand.runCommand (package:flutter_tools/src/commands/test.dart:479:7)
<asynchronous suspension>
#6      FlutterCommand.run.<anonymous closure> (package:flutter_tools/src/runner/flutter_command.dart:1563:27)
<asynchronous suspension>
#7      AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#8      CommandRunner.runCommand (package:args/command_runner.dart:212:13)
<asynchronous suspension>
#9      FlutterCommandRunner.runCommand.<anonymous closure> (package:flutter_tools/src/runner/flutter_command_runner.dart:494:9)
<asynchronous suspension>
#10     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#11     FlutterCommandRunner.runCommand (package:flutter_tools/src/runner/flutter_command_runner.dart:431:5)
<asynchronous suspension>
#12     run.<anonymous closure>.<anonymous closure> (package:flutter_tools/runner.dart:98:11)
<asynchronous suspension>
#13     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#14     main (package:flutter_tools/executable.dart:102:3)
<asynchronous suspension>
```

## flutter doctor

```
[32m[✓][39m Flutter (Channel stable, 3.32.5, on Microsoft Windows [Version 10.0.26100.4946], locale en-US) [539ms]
    [32m•[39m Flutter version 3.32.5 on channel stable at C:\FlutterDev\flutter
    [32m•[39m Upstream repository https://github.com/flutter/flutter.git
    [32m•[39m Framework revision fcf2c11572 (9 weeks ago), 2025-06-24 11:44:07 -0700
    [32m•[39m Engine revision dd93de6fb1
    [32m•[39m Dart version 3.8.1
    [32m•[39m DevTools version 2.45.1

[32m[✓][39m Windows Version (11 Home Single Language 64-bit, 24H2, 2009) [3.2s]

[33m[!][39m Android toolchain - develop for Android devices (Android SDK version 35.0.0) [5.3s]
    [32m•[39m Android SDK at C:\Users\<USER>\AppData\Local\Android\sdk
    [32m•[39m Platform android-35, build-tools 35.0.0
    [32m•[39m Java binary at: C:\Program Files\Android\Android Studio6\jbr\bin\java
      This is the JDK bundled with the latest Android Studio installation on this machine.
      To manually set the JDK path, use: `flutter config --jdk-dir="path/to/jdk"`.
    [32m•[39m Java version OpenJDK Runtime Environment (build 21.0.6+-13368085-b895.109)
    [33m![39m Some Android licenses not accepted. To resolve this, run: flutter doctor --android-licenses

[32m[✓][39m Chrome - develop for the web [38ms]
    [32m•[39m Chrome at C:\Program Files\Google\Chrome\Application\chrome.exe

[31m[✗][39m Visual Studio - develop Windows apps [35ms]
    [31m✗[39m Visual Studio not installed; this is necessary to develop Windows apps.
      Download at https://visualstudio.microsoft.com/downloads/.
      Please install the "Desktop development with C++" workload, including all of its default components

[33m[!][39m Android Studio (version 2022.2) [28ms]
    [32m•[39m Android Studio at C:\Program Files\Android\Android Studio2
    [32m•[39m Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    [32m•[39m Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart
    [31m✗[39m Unable to determine bundled Java version.
    [32m•[39m Try updating or re-installing Android Studio.

[32m[✓][39m Android Studio (version 2024.3.2) [22ms]
    [32m•[39m Android Studio at C:\Program Files\Android\Android Studio6
    [32m•[39m Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    [32m•[39m Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart
    [32m•[39m Java version OpenJDK Runtime Environment (build 21.0.6+-13368085-b895.109)

[32m[✓][39m VS Code (version 1.101.2) [22ms]
    [32m•[39m VS Code at C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code
    [32m•[39m Flutter extension version 3.114.0

[32m[✓][39m Connected device (4 available) [496ms]
    [32m•[39m sdk gphone64 x86 64 (mobile) • emulator-5554 • android-x64    • Android 15 (API 35) (emulator)
    [32m•[39m Windows (desktop)            • windows       • windows-x64    • Microsoft Windows [Version 10.0.26100.4946]
    [32m•[39m Chrome (web)                 • chrome        • web-javascript • Google Chrome 139.0.7258.154
    [32m•[39m Edge (web)                   • edge          • web-javascript • Microsoft Edge 139.0.3405.125

[32m[✓][39m Network resources [1,587ms]
    [32m•[39m All expected network resources are available.

[33m![39m Doctor found issues in 3 categories.
```
