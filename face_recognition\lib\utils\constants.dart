import 'package:flutter/material.dart';

class AppConstants {
  // App Information
  static const String appName = 'Face Recognition Attendance';
  static const String appVersion = '1.0.0';
  
  // Face Recognition Settings
  static const double defaultRecognitionThreshold = 0.65;
  static const double strictRecognitionThreshold = 0.55;
  static const double looseRecognitionThreshold = 0.75;
  
  // Face Detection Settings
  static const double minFaceSize = 0.1; // Minimum face size relative to image
  static const double maxFaceSize = 1.0; // Maximum face size relative to image
  static const double minFaceQuality = 0.5; // Minimum face quality score
  
  // Camera Settings
  static const int cameraPreviewWidth = 640;
  static const int cameraPreviewHeight = 480;
  static const int captureImageWidth = 224;
  static const int captureImageHeight = 224;
  
  // Database Settings
  static const String databaseName = 'face_recognition.db';
  static const int databaseVersion = 1;
  
  // Table Names
  static const String studentsTable = 'students';
  static const String attendanceTable = 'attendance';
  static const String faceEmbeddingsTable = 'face_embeddings';
  static const String settingsTable = 'settings';
  
  // API Settings
  static const String baseUrl = 'https://your-api-domain.com/api';
  static const int apiTimeoutSeconds = 30;
  static const int maxRetryAttempts = 3;
  
  // Storage Keys
  static const String recognitionThresholdKey = 'recognition_threshold';
  static const String autoSyncKey = 'auto_sync';
  static const String lastSyncKey = 'last_sync';
  static const String offlineModeKey = 'offline_mode';
  static const String cameraQualityKey = 'camera_quality';
  
  // File Paths
  static const String modelsPath = 'assets/models/';
  static const String mobileFaceNetModel = 'mobilefacenet.tflite';
  static const String faceDetectionModel = 'face_detection.tflite';
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double buttonHeight = 48.0;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);
  
  // Colors
  static const Color primaryColor = Color(0xFF2196F3);
  static const Color secondaryColor = Color(0xFF03DAC6);
  static const Color errorColor = Color(0xFFB00020);
  static const Color successColor = Color(0xFF4CAF50);
  static const Color warningColor = Color(0xFFFF9800);
  
  // Face Detection Colors
  static const Color faceDetectionColor = Color(0xFF00FF00);
  static const Color faceRecognitionColor = Color(0xFF0000FF);
  static const Color noFaceColor = Color(0xFFFF0000);
  
  // Attendance Status Colors
  static const Color presentColor = Color(0xFF4CAF50);
  static const Color absentColor = Color(0xFFB00020);
  static const Color lateColor = Color(0xFFFF9800);
  
  // Text Styles
  static const TextStyle headingStyle = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
  );
  
  static const TextStyle subheadingStyle = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
  );
  
  static const TextStyle bodyStyle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
  );
  
  static const TextStyle captionStyle = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
  );
  
  // Error Messages
  static const String cameraPermissionError = 'Camera permission is required for face recognition';
  static const String storagePermissionError = 'Storage permission is required to save data';
  static const String noFaceDetectedError = 'No face detected. Please position your face in the camera';
  static const String multipleFacesError = 'Multiple faces detected. Please ensure only one person is in the camera';
  static const String lowQualityFaceError = 'Face quality is too low. Please improve lighting and face the camera directly';
  static const String recognitionFailedError = 'Face recognition failed. Please try again';
  static const String networkError = 'Network connection error. Operating in offline mode';
  static const String syncError = 'Failed to sync data. Will retry automatically';
  
  // Success Messages
  static const String attendanceMarkedSuccess = 'Attendance marked successfully';
  static const String studentRegisteredSuccess = 'Student registered successfully';
  static const String dataSyncedSuccess = 'Data synced successfully';
  
  // Validation Rules
  static const int minNameLength = 2;
  static const int maxNameLength = 50;
  static const int minEmailLength = 5;
  static const int maxEmailLength = 100;
  
  // Face Recognition Thresholds by Quality
  static const Map<String, double> qualityThresholds = {
    'high': 0.55,
    'medium': 0.65,
    'low': 0.75,
  };
  
  // Sync Settings
  static const Duration autoSyncInterval = Duration(minutes: 15);
  static const int maxOfflineRecords = 1000;
  static const Duration maxOfflineTime = Duration(days: 7);
  
  // Performance Settings
  static const int maxConcurrentRecognitions = 1;
  static const Duration recognitionTimeout = Duration(seconds: 5);
  static const int maxFaceEmbeddingsPerStudent = 5;
  
  // Debug Settings
  static const bool enableDebugMode = true;
  static const bool enablePerformanceLogging = true;
  static const bool enableFaceDetectionOverlay = true;
}

// Enum for recognition quality levels
enum RecognitionQuality {
  high,
  medium,
  low,
}

// Enum for app themes
enum AppTheme {
  light,
  dark,
  system,
}

// Enum for camera quality
enum CameraQuality {
  low,
  medium,
  high,
  veryHigh,
}

// Extension methods for enums
extension RecognitionQualityExtension on RecognitionQuality {
  double get threshold {
    switch (this) {
      case RecognitionQuality.high:
        return AppConstants.qualityThresholds['high']!;
      case RecognitionQuality.medium:
        return AppConstants.qualityThresholds['medium']!;
      case RecognitionQuality.low:
        return AppConstants.qualityThresholds['low']!;
    }
  }
  
  String get displayName {
    switch (this) {
      case RecognitionQuality.high:
        return 'High (Most Secure)';
      case RecognitionQuality.medium:
        return 'Medium (Balanced)';
      case RecognitionQuality.low:
        return 'Low (Most Permissive)';
    }
  }
}

extension CameraQualityExtension on CameraQuality {
  String get displayName {
    switch (this) {
      case CameraQuality.low:
        return 'Low (Faster)';
      case CameraQuality.medium:
        return 'Medium';
      case CameraQuality.high:
        return 'High';
      case CameraQuality.veryHigh:
        return 'Very High (Slower)';
    }
  }
}
