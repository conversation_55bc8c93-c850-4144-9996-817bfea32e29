{"roots": ["face_recognition"], "packages": [{"name": "face_recognition", "version": "1.0.0+1", "dependencies": ["camera", "connectivity_plus", "crypto", "cupertino_icons", "dio", "flutter", "flutter_secure_storage", "google_ml_kit", "http", "image", "intl", "path", "path_provider", "permission_handler", "provider", "shared_preferences", "sqflite", "tflite_flutter", "tflite_flutter_helper"], "devDependencies": ["flutter_lints", "flutter_test"]}, {"name": "flutter_lints", "version": "5.0.0", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "flutter_secure_storage", "version": "9.2.4", "dependencies": ["flutter", "flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_platform_interface", "flutter_secure_storage_web", "flutter_secure_storage_windows", "meta"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "intl", "version": "0.19.0", "dependencies": ["clock", "meta", "path"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "permission_handler", "version": "11.4.0", "dependencies": ["flutter", "meta", "permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_platform_interface", "permission_handler_windows"]}, {"name": "provider", "version": "6.1.5+1", "dependencies": ["collection", "flutter", "nested"]}, {"name": "connectivity_plus", "version": "5.0.2", "dependencies": ["connectivity_plus_platform_interface", "flutter", "flutter_web_plugins", "js", "meta", "nm"]}, {"name": "dio", "version": "5.9.0", "dependencies": ["async", "collection", "dio_web_adapter", "http_parser", "meta", "mime", "path"]}, {"name": "http", "version": "1.5.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "sqflite", "version": "2.4.2", "dependencies": ["flutter", "path", "sqflite_android", "sqflite_common", "sqflite_darwin", "sqflite_platform_interface"]}, {"name": "tflite_flutter_helper", "version": "0.2.1", "dependencies": ["flutter", "image", "meta", "path_provider", "quiver", "tflite_flutter", "tuple"]}, {"name": "tflite_flutter", "version": "0.9.5", "dependencies": ["ffi", "flutter", "path", "quiver"]}, {"name": "google_ml_kit", "version": "0.16.3", "dependencies": ["flutter", "google_mlkit_barcode_scanning", "google_mlkit_commons", "google_mlkit_digital_ink_recognition", "google_mlkit_entity_extraction", "google_mlkit_face_detection", "google_mlkit_face_mesh_detection", "google_mlkit_image_labeling", "google_mlkit_language_id", "google_mlkit_object_detection", "google_mlkit_pose_detection", "google_mlkit_selfie_segmentation", "google_mlkit_smart_reply", "google_mlkit_text_recognition", "google_mlkit_translation"]}, {"name": "image", "version": "3.3.0", "dependencies": ["archive", "meta", "xml"]}, {"name": "camera", "version": "0.9.8+1", "dependencies": ["camera_android", "camera_avfoundation", "camera_platform_interface", "camera_web", "flutter", "flutter_plugin_android_lifecycle", "quiver"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "lints", "version": "5.1.1", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "flutter_secure_storage_windows", "version": "3.1.2", "dependencies": ["ffi", "flutter", "flutter_secure_storage_platform_interface", "path", "path_provider", "win32"]}, {"name": "flutter_secure_storage_web", "version": "1.2.1", "dependencies": ["flutter", "flutter_secure_storage_platform_interface", "flutter_web_plugins", "js"]}, {"name": "flutter_secure_storage_platform_interface", "version": "1.1.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_secure_storage_macos", "version": "3.1.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "flutter_secure_storage_linux", "version": "1.2.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.2", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.18", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "permission_handler_platform_interface", "version": "4.3.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_windows", "version": "0.2.1", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_html", "version": "0.1.3+5", "dependencies": ["flutter", "flutter_web_plugins", "permission_handler_platform_interface", "web"]}, {"name": "permission_handler_apple", "version": "9.4.7", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_android", "version": "12.1.0", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "nm", "version": "0.5.0", "dependencies": ["dbus"]}, {"name": "js", "version": "0.6.7", "dependencies": ["meta"]}, {"name": "connectivity_plus_platform_interface", "version": "1.2.4", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "dio_web_adapter", "version": "2.1.1", "dependencies": ["dio", "http_parser", "meta", "web"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.12", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "sqflite_common", "version": "2.5.6", "dependencies": ["meta", "path", "synchronized"]}, {"name": "sqflite_platform_interface", "version": "2.4.0", "dependencies": ["flutter", "meta", "platform", "plugin_platform_interface", "sqflite_common"]}, {"name": "sqflite_darwin", "version": "2.4.2", "dependencies": ["flutter", "meta", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "sqflite_android", "version": "2.4.1", "dependencies": ["flutter", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "tuple", "version": "2.0.2", "dependencies": []}, {"name": "quiver", "version": "3.2.2", "dependencies": ["matcher"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "google_mlkit_smart_reply", "version": "0.9.0", "dependencies": ["flutter"]}, {"name": "google_mlkit_translation", "version": "0.9.0", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_language_id", "version": "0.9.0", "dependencies": ["flutter"]}, {"name": "google_mlkit_entity_extraction", "version": "0.11.0", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_text_recognition", "version": "0.11.0", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_selfie_segmentation", "version": "0.6.0", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_pose_detection", "version": "0.10.0", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_object_detection", "version": "0.11.0", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_image_labeling", "version": "0.10.0", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_face_mesh_detection", "version": "0.0.2", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_face_detection", "version": "0.9.0", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_digital_ink_recognition", "version": "0.10.0", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_barcode_scanning", "version": "0.10.0", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "google_mlkit_commons", "version": "0.6.1", "dependencies": ["flutter"]}, {"name": "xml", "version": "6.6.1", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "archive", "version": "3.6.1", "dependencies": ["crypto", "path"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.30", "dependencies": ["flutter"]}, {"name": "camera_web", "version": "0.2.1+6", "dependencies": ["camera_platform_interface", "flutter", "flutter_web_plugins", "stream_transform"]}, {"name": "camera_platform_interface", "version": "2.10.0", "dependencies": ["cross_file", "flutter", "plugin_platform_interface", "stream_transform"]}, {"name": "camera_avfoundation", "version": "0.9.21+2", "dependencies": ["camera_platform_interface", "flutter", "stream_transform"]}, {"name": "camera_android", "version": "0.9.8+3", "dependencies": ["camera_platform_interface", "flutter", "flutter_plugin_android_lifecycle", "stream_transform"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "win32", "version": "5.14.0", "dependencies": ["ffi"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "synchronized", "version": "3.4.0", "dependencies": []}, {"name": "petitparser", "version": "7.0.1", "dependencies": ["collection", "meta"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "args", "version": "2.7.0", "dependencies": []}], "configVersion": 1}