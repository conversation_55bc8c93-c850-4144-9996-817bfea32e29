// This is a basic test file for the Face Recognition Attendance app.
// These tests verify that the main components can be instantiated correctly.

import 'package:flutter_test/flutter_test.dart';
import 'package:face_recognition/main.dart';

void main() {
  group('Face Recognition App Unit Tests', () {
    test('FaceRecognitionApp can be instantiated', () {
      // Test that the main app widget can be created
      const app = FaceRecognitionApp();
      expect(app, isNotNull);
      expect(app.runtimeType.toString(), 'FaceRecognitionApp');
    });

    test('SplashScreen can be instantiated', () {
      // Test that the splash screen widget can be created
      const splashScreen = SplashScreen();
      expect(splashScreen, isNotNull);
      expect(splashScreen.runtimeType.toString(), 'SplashScreen');
    });

    test('App constants are properly defined', () {
      // Test that we can import and access the app without runtime errors
      // This is a basic smoke test to ensure the app structure is valid
      expect(() => const FaceRecognitionApp(), returnsNormally);
      expect(() => const SplashScreen(), returnsNormally);
    });
  });
}
