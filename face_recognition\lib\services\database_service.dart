import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/student.dart';
import '../models/attendance.dart';
import '../models/face_embedding.dart';
import '../utils/constants.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), AppConstants.databaseName);
    
    return await openDatabase(
      path,
      version: AppConstants.databaseVersion,
      onCreate: _createDatabase,
      onUpgrade: _upgradeDatabase,
    );
  }

  Future<void> _createDatabase(Database db, int version) async {
    // Create students table
    await db.execute('''
      CREATE TABLE ${AppConstants.studentsTable} (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT NOT NULL UNIQUE,
        className TEXT NOT NULL,
        photoPath TEXT,
        faceEmbeddings TEXT,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER
      )
    ''');

    // Create attendance table
    await db.execute('''
      CREATE TABLE ${AppConstants.attendanceTable} (
        id TEXT PRIMARY KEY,
        studentId TEXT NOT NULL,
        studentName TEXT NOT NULL,
        className TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        status TEXT NOT NULL,
        method TEXT NOT NULL,
        confidence REAL,
        location TEXT,
        notes TEXT,
        synced INTEGER NOT NULL DEFAULT 0,
        syncedAt INTEGER,
        FOREIGN KEY (studentId) REFERENCES ${AppConstants.studentsTable} (id)
      )
    ''');

    // Create face embeddings table
    await db.execute('''
      CREATE TABLE ${AppConstants.faceEmbeddingsTable} (
        id TEXT PRIMARY KEY,
        studentId TEXT NOT NULL,
        embedding TEXT NOT NULL,
        quality REAL NOT NULL,
        createdAt INTEGER NOT NULL,
        imagePath TEXT,
        FOREIGN KEY (studentId) REFERENCES ${AppConstants.studentsTable} (id)
      )
    ''');

    // Create settings table
    await db.execute('''
      CREATE TABLE ${AppConstants.settingsTable} (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL
      )
    ''');

    // Create indexes for better performance
    await db.execute('''
      CREATE INDEX idx_attendance_student_id ON ${AppConstants.attendanceTable} (studentId)
    ''');
    
    await db.execute('''
      CREATE INDEX idx_attendance_timestamp ON ${AppConstants.attendanceTable} (timestamp)
    ''');
    
    await db.execute('''
      CREATE INDEX idx_face_embeddings_student_id ON ${AppConstants.faceEmbeddingsTable} (studentId)
    ''');
  }

  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < 2) {
      // Add new columns or tables for version 2
    }
  }

  // Student operations
  Future<int> insertStudent(Student student) async {
    final db = await database;
    return await db.insert(
      AppConstants.studentsTable,
      student.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<Student>> getAllStudents() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.studentsTable,
      orderBy: 'name ASC',
    );
    return List.generate(maps.length, (i) => Student.fromMap(maps[i]));
  }

  Future<Student?> getStudentById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.studentsTable,
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Student.fromMap(maps.first);
    }
    return null;
  }

  Future<Student?> getStudentByEmail(String email) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.studentsTable,
      where: 'email = ?',
      whereArgs: [email],
    );
    if (maps.isNotEmpty) {
      return Student.fromMap(maps.first);
    }
    return null;
  }

  Future<List<Student>> getStudentsByClass(String className) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.studentsTable,
      where: 'className = ?',
      whereArgs: [className],
      orderBy: 'name ASC',
    );
    return List.generate(maps.length, (i) => Student.fromMap(maps[i]));
  }

  Future<int> updateStudent(Student student) async {
    final db = await database;
    return await db.update(
      AppConstants.studentsTable,
      student.toMap(),
      where: 'id = ?',
      whereArgs: [student.id],
    );
  }

  Future<int> deleteStudent(String id) async {
    final db = await database;
    // Also delete related face embeddings and attendance records
    await db.delete(
      AppConstants.faceEmbeddingsTable,
      where: 'studentId = ?',
      whereArgs: [id],
    );
    await db.delete(
      AppConstants.attendanceTable,
      where: 'studentId = ?',
      whereArgs: [id],
    );
    return await db.delete(
      AppConstants.studentsTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Attendance operations
  Future<int> insertAttendance(Attendance attendance) async {
    final db = await database;
    return await db.insert(
      AppConstants.attendanceTable,
      attendance.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<Attendance>> getAllAttendance() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.attendanceTable,
      orderBy: 'timestamp DESC',
    );
    return List.generate(maps.length, (i) => Attendance.fromMap(maps[i]));
  }

  Future<List<Attendance>> getAttendanceByStudent(String studentId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.attendanceTable,
      where: 'studentId = ?',
      whereArgs: [studentId],
      orderBy: 'timestamp DESC',
    );
    return List.generate(maps.length, (i) => Attendance.fromMap(maps[i]));
  }

  Future<List<Attendance>> getAttendanceByDate(DateTime date) async {
    final db = await database;
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));
    
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.attendanceTable,
      where: 'timestamp >= ? AND timestamp < ?',
      whereArgs: [startOfDay.millisecondsSinceEpoch, endOfDay.millisecondsSinceEpoch],
      orderBy: 'timestamp DESC',
    );
    return List.generate(maps.length, (i) => Attendance.fromMap(maps[i]));
  }

  Future<List<Attendance>> getUnsyncedAttendance() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.attendanceTable,
      where: 'synced = ?',
      whereArgs: [0],
      orderBy: 'timestamp ASC',
    );
    return List.generate(maps.length, (i) => Attendance.fromMap(maps[i]));
  }

  Future<int> markAttendanceAsSynced(String attendanceId) async {
    final db = await database;
    return await db.update(
      AppConstants.attendanceTable,
      {
        'synced': 1,
        'syncedAt': DateTime.now().millisecondsSinceEpoch,
      },
      where: 'id = ?',
      whereArgs: [attendanceId],
    );
  }

  // Face embedding operations
  Future<int> insertFaceEmbedding(FaceEmbedding embedding) async {
    final db = await database;
    return await db.insert(
      AppConstants.faceEmbeddingsTable,
      embedding.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<FaceEmbedding>> getFaceEmbeddingsByStudent(String studentId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.faceEmbeddingsTable,
      where: 'studentId = ?',
      whereArgs: [studentId],
      orderBy: 'quality DESC',
    );
    return List.generate(maps.length, (i) => FaceEmbedding.fromMap(maps[i]));
  }

  Future<List<FaceEmbedding>> getAllFaceEmbeddings() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.faceEmbeddingsTable,
      orderBy: 'studentId, quality DESC',
    );
    return List.generate(maps.length, (i) => FaceEmbedding.fromMap(maps[i]));
  }

  // Settings operations
  Future<int> setSetting(String key, String value) async {
    final db = await database;
    return await db.insert(
      AppConstants.settingsTable,
      {'key': key, 'value': value},
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<String?> getSetting(String key) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.settingsTable,
      where: 'key = ?',
      whereArgs: [key],
    );
    if (maps.isNotEmpty) {
      return maps.first['value'] as String;
    }
    return null;
  }

  // Utility operations
  Future<int> getStudentCount() async {
    final db = await database;
    final result = await db.rawQuery('SELECT COUNT(*) FROM ${AppConstants.studentsTable}');
    return Sqflite.firstIntValue(result) ?? 0;
  }

  Future<int> getAttendanceCountToday() async {
    final now = DateTime.now();
    final startOfDay = DateTime(now.year, now.month, now.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));
    
    final db = await database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) FROM ${AppConstants.attendanceTable} WHERE timestamp >= ? AND timestamp < ?',
      [startOfDay.millisecondsSinceEpoch, endOfDay.millisecondsSinceEpoch],
    );
    return Sqflite.firstIntValue(result) ?? 0;
  }

  Future<void> clearAllData() async {
    final db = await database;
    await db.delete(AppConstants.attendanceTable);
    await db.delete(AppConstants.faceEmbeddingsTable);
    await db.delete(AppConstants.studentsTable);
    await db.delete(AppConstants.settingsTable);
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
