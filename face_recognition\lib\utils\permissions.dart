import 'package:permission_handler.dart';
import 'package:flutter/material.dart';

class PermissionHelper {
  // Check if camera permission is granted
  static Future<bool> hasCameraPermission() async {
    final status = await Permission.camera.status;
    return status.isGranted;
  }

  // Request camera permission
  static Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status.isGranted;
  }

  // Check if storage permission is granted
  static Future<bool> hasStoragePermission() async {
    final status = await Permission.storage.status;
    return status.isGranted;
  }

  // Request storage permission
  static Future<bool> requestStoragePermission() async {
    final status = await Permission.storage.request();
    return status.isGranted;
  }

  // Check if microphone permission is granted (for video recording if needed)
  static Future<bool> hasMicrophonePermission() async {
    final status = await Permission.microphone.status;
    return status.isGranted;
  }

  // Request microphone permission
  static Future<bool> requestMicrophonePermission() async {
    final status = await Permission.microphone.request();
    return status.isGranted;
  }

  // Check all required permissions
  static Future<Map<Permission, PermissionStatus>> checkAllPermissions() async {
    return await [
      Permission.camera,
      Permission.storage,
    ].request();
  }

  // Request all required permissions
  static Future<bool> requestAllPermissions() async {
    final permissions = await checkAllPermissions();
    
    bool allGranted = true;
    for (final permission in permissions.entries) {
      if (!permission.value.isGranted) {
        allGranted = false;
        break;
      }
    }
    
    return allGranted;
  }

  // Show permission dialog
  static Future<bool> showPermissionDialog(
    BuildContext context,
    String title,
    String message,
  ) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Grant Permission'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  // Show camera permission dialog
  static Future<bool> showCameraPermissionDialog(BuildContext context) async {
    return await showPermissionDialog(
      context,
      'Camera Permission Required',
      'This app needs camera access to detect and recognize faces for attendance marking. Please grant camera permission to continue.',
    );
  }

  // Show storage permission dialog
  static Future<bool> showStoragePermissionDialog(BuildContext context) async {
    return await showPermissionDialog(
      context,
      'Storage Permission Required',
      'This app needs storage access to save attendance data and face recognition models. Please grant storage permission to continue.',
    );
  }

  // Handle camera permission with dialog
  static Future<bool> handleCameraPermission(BuildContext context) async {
    // Check if permission is already granted
    if (await hasCameraPermission()) {
      return true;
    }

    // Show dialog explaining why permission is needed
    final shouldRequest = await showCameraPermissionDialog(context);
    if (!shouldRequest) {
      return false;
    }

    // Request permission
    final granted = await requestCameraPermission();
    
    if (!granted) {
      // Show dialog to go to settings if permission was denied
      await showPermissionDeniedDialog(context, 'Camera');
    }
    
    return granted;
  }

  // Handle storage permission with dialog
  static Future<bool> handleStoragePermission(BuildContext context) async {
    // Check if permission is already granted
    if (await hasStoragePermission()) {
      return true;
    }

    // Show dialog explaining why permission is needed
    final shouldRequest = await showStoragePermissionDialog(context);
    if (!shouldRequest) {
      return false;
    }

    // Request permission
    final granted = await requestStoragePermission();
    
    if (!granted) {
      // Show dialog to go to settings if permission was denied
      await showPermissionDeniedDialog(context, 'Storage');
    }
    
    return granted;
  }

  // Show permission denied dialog with option to go to settings
  static Future<void> showPermissionDeniedDialog(
    BuildContext context,
    String permissionName,
  ) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('$permissionName Permission Denied'),
          content: Text(
            '$permissionName permission is required for this app to function properly. '
            'Please go to app settings and grant the permission manually.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
              child: const Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }

  // Check if permission is permanently denied
  static Future<bool> isPermissionPermanentlyDenied(Permission permission) async {
    final status = await permission.status;
    return status.isPermanentlyDenied;
  }

  // Handle all permissions at app startup
  static Future<bool> handleAppStartupPermissions(BuildContext context) async {
    try {
      // Check camera permission
      final cameraGranted = await handleCameraPermission(context);
      if (!cameraGranted) {
        return false;
      }

      // Check storage permission
      final storageGranted = await handleStoragePermission(context);
      if (!storageGranted) {
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('Error handling permissions: $e');
      return false;
    }
  }

  // Get permission status text for UI display
  static String getPermissionStatusText(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return 'Granted';
      case PermissionStatus.denied:
        return 'Denied';
      case PermissionStatus.restricted:
        return 'Restricted';
      case PermissionStatus.limited:
        return 'Limited';
      case PermissionStatus.permanentlyDenied:
        return 'Permanently Denied';
      default:
        return 'Unknown';
    }
  }

  // Get permission status color for UI display
  static Color getPermissionStatusColor(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return Colors.green;
      case PermissionStatus.denied:
      case PermissionStatus.permanentlyDenied:
        return Colors.red;
      case PermissionStatus.restricted:
      case PermissionStatus.limited:
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
}
