class Student {
  final String id;
  final String name;
  final String email;
  final String className;
  final String? photoPath;
  final List<List<double>> faceEmbeddings;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Student({
    required this.id,
    required this.name,
    required this.email,
    required this.className,
    this.photoPath,
    required this.faceEmbeddings,
    required this.createdAt,
    this.updatedAt,
  });

  // Convert Student to Map for database storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'className': className,
      'photoPath': photoPath,
      'faceEmbeddings': _embeddingsToString(faceEmbeddings),
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
    };
  }

  // Create Student from Map (database)
  factory Student.fromMap(Map<String, dynamic> map) {
    return Student(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      className: map['className'] ?? '',
      photoPath: map['photoPath'],
      faceEmbeddings: _embeddingsFromString(map['faceEmbeddings'] ?? ''),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      updatedAt: map['updatedAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['updatedAt'])
          : null,
    );
  }

  // Convert Student to JSON for API
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'className': className,
      'photoPath': photoPath,
      'faceEmbeddings': faceEmbeddings,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  // Create Student from JSON (API response)
  factory Student.fromJson(Map<String, dynamic> json) {
    return Student(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      className: json['className'] ?? '',
      photoPath: json['photoPath'],
      faceEmbeddings: (json['faceEmbeddings'] as List<dynamic>?)
          ?.map((e) => (e as List<dynamic>).map((x) => x.toDouble()).toList())
          .toList() ?? [],
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt'])
          : null,
    );
  }

  // Helper method to convert embeddings to string for database storage
  static String _embeddingsToString(List<List<double>> embeddings) {
    return embeddings.map((embedding) => embedding.join(',')).join(';');
  }

  // Helper method to convert string back to embeddings
  static List<List<double>> _embeddingsFromString(String embeddingsString) {
    if (embeddingsString.isEmpty) return [];
    return embeddingsString
        .split(';')
        .map((embedding) => embedding.split(',').map(double.parse).toList())
        .toList();
  }

  // Copy with method for updating student data
  Student copyWith({
    String? id,
    String? name,
    String? email,
    String? className,
    String? photoPath,
    List<List<double>>? faceEmbeddings,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Student(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      className: className ?? this.className,
      photoPath: photoPath ?? this.photoPath,
      faceEmbeddings: faceEmbeddings ?? this.faceEmbeddings,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Student{id: $id, name: $name, email: $email, className: $className}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Student && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
