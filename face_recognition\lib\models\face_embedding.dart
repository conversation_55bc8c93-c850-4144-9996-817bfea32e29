import 'dart:math';

class FaceEmbedding {
  final String id;
  final String studentId;
  final List<double> embedding;
  final double quality; // Face quality score from ML Kit
  final DateTime createdAt;
  final String? imagePath; // Optional path to the source image

  FaceEmbedding({
    required this.id,
    required this.studentId,
    required this.embedding,
    required this.quality,
    required this.createdAt,
    this.imagePath,
  });

  // Convert FaceEmbedding to Map for database storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'studentId': studentId,
      'embedding': embedding.join(','),
      'quality': quality,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'imagePath': imagePath,
    };
  }

  // Create FaceEmbedding from Map (database)
  factory FaceEmbedding.fromMap(Map<String, dynamic> map) {
    return FaceEmbedding(
      id: map['id'] ?? '',
      studentId: map['studentId'] ?? '',
      embedding: (map['embedding'] as String)
          .split(',')
          .map((e) => double.parse(e))
          .toList(),
      quality: map['quality']?.toDouble() ?? 0.0,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      imagePath: map['imagePath'],
    );
  }

  // Convert FaceEmbedding to JSON for API
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'studentId': studentId,
      'embedding': embedding,
      'quality': quality,
      'createdAt': createdAt.toIso8601String(),
      'imagePath': imagePath,
    };
  }

  // Create FaceEmbedding from JSON (API response)
  factory FaceEmbedding.fromJson(Map<String, dynamic> json) {
    return FaceEmbedding(
      id: json['id'] ?? '',
      studentId: json['studentId'] ?? '',
      embedding: (json['embedding'] as List<dynamic>)
          .map((e) => e.toDouble())
          .toList()
          .cast<double>(),
      quality: json['quality']?.toDouble() ?? 0.0,
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      imagePath: json['imagePath'],
    );
  }

  // Calculate cosine similarity between two embeddings
  static double cosineSimilarity(List<double> embedding1, List<double> embedding2) {
    if (embedding1.length != embedding2.length) {
      throw ArgumentError('Embeddings must have the same length');
    }

    double dotProduct = 0.0;
    double norm1 = 0.0;
    double norm2 = 0.0;

    for (int i = 0; i < embedding1.length; i++) {
      dotProduct += embedding1[i] * embedding2[i];
      norm1 += embedding1[i] * embedding1[i];
      norm2 += embedding2[i] * embedding2[i];
    }

    if (norm1 == 0.0 || norm2 == 0.0) {
      return 0.0;
    }

    return dotProduct / (sqrt(norm1) * sqrt(norm2));
  }

  // Calculate Euclidean distance between two embeddings
  static double euclideanDistance(List<double> embedding1, List<double> embedding2) {
    if (embedding1.length != embedding2.length) {
      throw ArgumentError('Embeddings must have the same length');
    }

    double sum = 0.0;
    for (int i = 0; i < embedding1.length; i++) {
      double diff = embedding1[i] - embedding2[i];
      sum += diff * diff;
    }

    return sqrt(sum);
  }

  // Compare this embedding with another using cosine similarity
  double compareWith(FaceEmbedding other) {
    return cosineSimilarity(embedding, other.embedding);
  }

  // Compare this embedding with a raw embedding vector
  double compareWithVector(List<double> otherEmbedding) {
    return cosineSimilarity(embedding, otherEmbedding);
  }

  // Check if this embedding matches another within a threshold
  bool matches(FaceEmbedding other, double threshold) {
    return compareWith(other) >= threshold;
  }

  // Check if this embedding matches a vector within a threshold
  bool matchesVector(List<double> otherEmbedding, double threshold) {
    return compareWithVector(otherEmbedding) >= threshold;
  }

  // Normalize the embedding vector (make it unit length)
  FaceEmbedding normalize() {
    double norm = sqrt(embedding.map((e) => e * e).reduce((a, b) => a + b));
    if (norm == 0.0) return this;

    List<double> normalizedEmbedding = embedding.map((e) => e / norm).toList();
    
    return FaceEmbedding(
      id: id,
      studentId: studentId,
      embedding: normalizedEmbedding,
      quality: quality,
      createdAt: createdAt,
      imagePath: imagePath,
    );
  }

  // Copy with method for updating embedding data
  FaceEmbedding copyWith({
    String? id,
    String? studentId,
    List<double>? embedding,
    double? quality,
    DateTime? createdAt,
    String? imagePath,
  }) {
    return FaceEmbedding(
      id: id ?? this.id,
      studentId: studentId ?? this.studentId,
      embedding: embedding ?? this.embedding,
      quality: quality ?? this.quality,
      createdAt: createdAt ?? this.createdAt,
      imagePath: imagePath ?? this.imagePath,
    );
  }

  @override
  String toString() {
    return 'FaceEmbedding{id: $id, studentId: $studentId, quality: $quality, embeddingLength: ${embedding.length}}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FaceEmbedding && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
