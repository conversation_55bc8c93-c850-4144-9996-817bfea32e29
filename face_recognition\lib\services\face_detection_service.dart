import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_ml_kit/google_ml_kit.dart';
import 'package:image/image.dart' as img;
import '../utils/constants.dart';

class FaceDetectionResult {
  final List<Face> faces;
  final Size imageSize;
  final img.Image? croppedFace;
  final double quality;
  final String message;

  FaceDetectionResult({
    required this.faces,
    required this.imageSize,
    this.croppedFace,
    required this.quality,
    required this.message,
  });
}

class FaceDetectionService {
  static final FaceDetectionService _instance = FaceDetectionService._internal();
  factory FaceDetectionService() => _instance;
  FaceDetectionService._internal();

  late FaceDetector _faceDetector;
  bool _isInitialized = false;

  // Initialize face detection service
  Future<void> initialize() async {
    try {
      _faceDetector = FaceDetector(
        options: FaceDetectorOptions(
          enableClassification: true,
          enableLandmarks: true,
          enableContours: true,
          enableTracking: true,
          minFaceSize: AppConstants.minFaceSize,
          performanceMode: FaceDetectorMode.accurate,
        ),
      );
      _isInitialized = true;
      debugPrint('Face detection service initialized');
    } catch (e) {
      debugPrint('Error initializing face detection service: $e');
      rethrow;
    }
  }

  // Detect faces in camera image
  Future<FaceDetectionResult> detectFacesInCameraImage(
    CameraImage cameraImage,
  ) async {
    if (!_isInitialized) {
      throw Exception('Face detection service not initialized');
    }

    try {
      // Convert camera image to InputImage
      final InputImage inputImage = _convertCameraImageToInputImage(cameraImage);
      
      // Detect faces
      final List<Face> faces = await _faceDetector.processImage(inputImage);
      
      // Convert camera image to img.Image for processing
      final img.Image? image = _convertCameraImageToImage(cameraImage);
      
      return _processFaceDetectionResult(
        faces,
        Size(cameraImage.width.toDouble(), cameraImage.height.toDouble()),
        image,
      );
    } catch (e) {
      debugPrint('Error detecting faces in camera image: $e');
      return FaceDetectionResult(
        faces: [],
        imageSize: Size(cameraImage.width.toDouble(), cameraImage.height.toDouble()),
        quality: 0.0,
        message: 'Error detecting faces: $e',
      );
    }
  }

  // Detect faces in image file
  Future<FaceDetectionResult> detectFacesInImage(String imagePath) async {
    if (!_isInitialized) {
      throw Exception('Face detection service not initialized');
    }

    try {
      final InputImage inputImage = InputImage.fromFilePath(imagePath);
      final List<Face> faces = await _faceDetector.processImage(inputImage);
      
      // Load image for processing
      final img.Image? image = img.decodeImage(await _readImageBytes(imagePath));
      
      return _processFaceDetectionResult(
        faces,
        image != null ? Size(image.width.toDouble(), image.height.toDouble()) : const Size(0, 0),
        image,
      );
    } catch (e) {
      debugPrint('Error detecting faces in image: $e');
      return FaceDetectionResult(
        faces: [],
        imageSize: const Size(0, 0),
        quality: 0.0,
        message: 'Error detecting faces: $e',
      );
    }
  }

  // Process face detection result
  FaceDetectionResult _processFaceDetectionResult(
    List<Face> faces,
    Size imageSize,
    img.Image? image,
  ) {
    if (faces.isEmpty) {
      return FaceDetectionResult(
        faces: faces,
        imageSize: imageSize,
        quality: 0.0,
        message: AppConstants.noFaceDetectedError,
      );
    }

    if (faces.length > 1) {
      return FaceDetectionResult(
        faces: faces,
        imageSize: imageSize,
        quality: 0.0,
        message: AppConstants.multipleFacesError,
      );
    }

    final Face face = faces.first;
    final double quality = _calculateFaceQuality(face, imageSize);
    
    if (quality < AppConstants.minFaceQuality) {
      return FaceDetectionResult(
        faces: faces,
        imageSize: imageSize,
        quality: quality,
        message: AppConstants.lowQualityFaceError,
      );
    }

    // Crop face from image
    img.Image? croppedFace;
    if (image != null) {
      croppedFace = _cropFaceFromImage(image, face);
    }

    return FaceDetectionResult(
      faces: faces,
      imageSize: imageSize,
      croppedFace: croppedFace,
      quality: quality,
      message: 'Face detected successfully',
    );
  }

  // Calculate face quality score
  double _calculateFaceQuality(Face face, Size imageSize) {
    double quality = 1.0;

    // Check face size (should be reasonable portion of image)
    final double faceArea = face.boundingBox.width * face.boundingBox.height;
    final double imageArea = imageSize.width * imageSize.height;
    final double faceRatio = faceArea / imageArea;
    
    if (faceRatio < 0.05) {
      quality *= 0.5; // Face too small
    } else if (faceRatio > 0.8) {
      quality *= 0.7; // Face too large
    }

    // Check if face is centered
    final double faceCenterX = face.boundingBox.center.dx;
    final double faceCenterY = face.boundingBox.center.dy;
    final double imageCenterX = imageSize.width / 2;
    final double imageCenterY = imageSize.height / 2;
    
    final double centerDistance = ((faceCenterX - imageCenterX).abs() + (faceCenterY - imageCenterY).abs()) / 2;
    final double maxDistance = (imageSize.width + imageSize.height) / 4;
    final double centerScore = 1.0 - (centerDistance / maxDistance).clamp(0.0, 1.0);
    quality *= (0.7 + 0.3 * centerScore);

    // Check head pose (prefer frontal faces)
    if (face.headEulerAngleY != null && face.headEulerAngleZ != null) {
      final double yawAngle = face.headEulerAngleY!.abs();
      final double rollAngle = face.headEulerAngleZ!.abs();
      
      if (yawAngle > 30 || rollAngle > 30) {
        quality *= 0.6; // Head turned too much
      } else if (yawAngle > 15 || rollAngle > 15) {
        quality *= 0.8; // Slight head turn
      }
    }

    // Check if eyes are open (if classification is available)
    if (face.leftEyeOpenProbability != null && face.rightEyeOpenProbability != null) {
      final double leftEyeOpen = face.leftEyeOpenProbability!;
      final double rightEyeOpen = face.rightEyeOpenProbability!;
      
      if (leftEyeOpen < 0.5 || rightEyeOpen < 0.5) {
        quality *= 0.7; // Eyes closed or partially closed
      }
    }

    // Check smile (optional - might be too strict for attendance)
    if (face.smilingProbability != null) {
      final double smiling = face.smilingProbability!;
      // Neutral expression is fine for attendance
      if (smiling > 0.8) {
        quality *= 1.1; // Slight bonus for clear expression
      }
    }

    return quality.clamp(0.0, 1.0);
  }

  // Crop face from image
  img.Image? _cropFaceFromImage(img.Image image, Face face) {
    try {
      final Rect boundingBox = face.boundingBox;
      
      // Add padding around face
      const double padding = 0.2; // 20% padding
      final double paddingX = boundingBox.width * padding;
      final double paddingY = boundingBox.height * padding;
      
      final int x = (boundingBox.left - paddingX).round().clamp(0, image.width);
      final int y = (boundingBox.top - paddingY).round().clamp(0, image.height);
      final int width = (boundingBox.width + 2 * paddingX).round().clamp(0, image.width - x);
      final int height = (boundingBox.height + 2 * paddingY).round().clamp(0, image.height - y);
      
      final img.Image croppedImage = img.copyCrop(
        image,
        x,
        y,
        width,
        height,
      );
      
      // Resize to standard size for face recognition
      return img.copyResize(
        croppedImage,
        width: AppConstants.captureImageWidth,
        height: AppConstants.captureImageHeight,
      );
    } catch (e) {
      debugPrint('Error cropping face: $e');
      return null;
    }
  }

  // Convert CameraImage to InputImage
  InputImage _convertCameraImageToInputImage(CameraImage cameraImage) {
    final WriteBuffer allBytes = WriteBuffer();
    for (final Plane plane in cameraImage.planes) {
      allBytes.putUint8List(plane.bytes);
    }
    final bytes = allBytes.done().buffer.asUint8List();

    final Size imageSize = Size(cameraImage.width.toDouble(), cameraImage.height.toDouble());

    final InputImageRotation imageRotation = InputImageRotation.rotation0deg;

    final InputImageFormat inputImageFormat = InputImageFormat.nv21;

    return InputImage.fromBytes(
      bytes: bytes,
      metadata: InputImageMetadata(
        size: imageSize,
        rotation: imageRotation,
        format: inputImageFormat,
        bytesPerRow: cameraImage.planes[0].bytesPerRow,
      ),
    );
  }

  // Convert CameraImage to img.Image
  img.Image? _convertCameraImageToImage(CameraImage cameraImage) {
    try {
      if (cameraImage.format.group == ImageFormatGroup.yuv420) {
        return _convertYUV420ToImage(cameraImage);
      } else if (cameraImage.format.group == ImageFormatGroup.bgra8888) {
        return _convertBGRA8888ToImage(cameraImage);
      }
      return null;
    } catch (e) {
      debugPrint('Error converting camera image: $e');
      return null;
    }
  }

  // Convert YUV420 to img.Image
  img.Image _convertYUV420ToImage(CameraImage cameraImage) {
    final int width = cameraImage.width;
    final int height = cameraImage.height;
    
    final int uvRowStride = cameraImage.planes[1].bytesPerRow;
    final int uvPixelStride = cameraImage.planes[1].bytesPerPixel!;
    
    final image = img.Image(width, height);

    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final int uvIndex = uvPixelStride * (x / 2).floor() + uvRowStride * (y / 2).floor();
        final int index = y * width + x;

        final yp = cameraImage.planes[0].bytes[index];
        final up = cameraImage.planes[1].bytes[uvIndex];
        final vp = cameraImage.planes[2].bytes[uvIndex];

        int r = (yp + vp * 1436 / 1024 - 179).round().clamp(0, 255);
        int g = (yp - up * 46549 / 131072 + 44 - vp * 93604 / 131072 + 91).round().clamp(0, 255);
        int b = (yp + up * 1814 / 1024 - 227).round().clamp(0, 255);

        image.setPixel(x, y, img.getColor(r, g, b));
      }
    }
    
    return image;
  }

  // Convert BGRA8888 to img.Image
  img.Image _convertBGRA8888ToImage(CameraImage cameraImage) {
    final int width = cameraImage.width;
    final int height = cameraImage.height;
    final Uint8List bytes = cameraImage.planes[0].bytes;
    
    final image = img.Image(width, height);

    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final int index = (y * width + x) * 4;
        final int b = bytes[index];
        final int g = bytes[index + 1];
        final int r = bytes[index + 2];
        final int a = bytes[index + 3];

        image.setPixel(x, y, img.getColor(r, g, b, a));
      }
    }
    
    return image;
  }

  // Read image bytes from file
  Future<Uint8List> _readImageBytes(String imagePath) async {
    final file = await DefaultAssetBundle.of(WidgetsBinding.instance.rootElement!).load(imagePath);
    return file.buffer.asUint8List();
  }

  // Check if face detection is available
  bool get isInitialized => _isInitialized;

  // Dispose face detection service
  Future<void> dispose() async {
    try {
      await _faceDetector.close();
      _isInitialized = false;
      debugPrint('Face detection service disposed');
    } catch (e) {
      debugPrint('Error disposing face detection service: $e');
    }
  }
}
