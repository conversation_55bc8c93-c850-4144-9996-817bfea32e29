import 'dart:async';
import 'dart:typed_data';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;

class CameraService {
  static final CameraService _instance = CameraService._internal();
  factory CameraService() => _instance;
  CameraService._internal();

  CameraController? _controller;
  List<CameraDescription>? _cameras;
  bool _isInitialized = false;
  StreamController<CameraImage>? _imageStreamController;

  // Getters
  CameraController? get controller => _controller;
  bool get isInitialized => _isInitialized;
  List<CameraDescription>? get cameras => _cameras;

  // Initialize camera service
  Future<bool> initialize() async {
    try {
      _cameras = await availableCameras();
      if (_cameras == null || _cameras!.isEmpty) {
        debugPrint('No cameras available');
        return false;
      }

      // Use front camera for face recognition (index 1 usually)
      // Fall back to first available camera if front camera not found
      CameraDescription selectedCamera = _cameras!.first;
      for (final camera in _cameras!) {
        if (camera.lensDirection == CameraLensDirection.front) {
          selectedCamera = camera;
          break;
        }
      }

      _controller = CameraController(
        selectedCamera,
        ResolutionPreset.medium,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.yuv420,
      );

      await _controller!.initialize();
      _isInitialized = true;
      
      debugPrint('Camera initialized successfully');
      return true;
    } catch (e) {
      debugPrint('Error initializing camera: $e');
      return false;
    }
  }

  // Start image stream for real-time processing
  Future<void> startImageStream(Function(CameraImage) onImage) async {
    if (!_isInitialized || _controller == null) {
      throw Exception('Camera not initialized');
    }

    try {
      await _controller!.startImageStream(onImage);
      debugPrint('Image stream started');
    } catch (e) {
      debugPrint('Error starting image stream: $e');
      rethrow;
    }
  }

  // Stop image stream
  Future<void> stopImageStream() async {
    if (_controller != null && _controller!.value.isStreamingImages) {
      try {
        await _controller!.stopImageStream();
        debugPrint('Image stream stopped');
      } catch (e) {
        debugPrint('Error stopping image stream: $e');
      }
    }
  }

  // Capture a photo
  Future<XFile?> capturePhoto() async {
    if (!_isInitialized || _controller == null) {
      throw Exception('Camera not initialized');
    }

    try {
      final XFile photo = await _controller!.takePicture();
      debugPrint('Photo captured: ${photo.path}');
      return photo;
    } catch (e) {
      debugPrint('Error capturing photo: $e');
      return null;
    }
  }

  // Convert CameraImage to Image for processing
  static img.Image? convertCameraImage(CameraImage cameraImage) {
    try {
      if (cameraImage.format.group == ImageFormatGroup.yuv420) {
        return _convertYUV420ToImage(cameraImage);
      } else if (cameraImage.format.group == ImageFormatGroup.bgra8888) {
        return _convertBGRA8888ToImage(cameraImage);
      } else {
        debugPrint('Unsupported image format: ${cameraImage.format.group}');
        return null;
      }
    } catch (e) {
      debugPrint('Error converting camera image: $e');
      return null;
    }
  }

  // Convert YUV420 to Image
  static img.Image _convertYUV420ToImage(CameraImage cameraImage) {
    final int width = cameraImage.width;
    final int height = cameraImage.height;

    final int uvRowStride = cameraImage.planes[1].bytesPerRow;
    final int uvPixelStride = cameraImage.planes[1].bytesPerPixel!;

    final image = img.Image(width, height);

    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final int uvIndex = uvPixelStride * (x / 2).floor() + uvRowStride * (y / 2).floor();
        final int index = y * width + x;

        final yp = cameraImage.planes[0].bytes[index];
        final up = cameraImage.planes[1].bytes[uvIndex];
        final vp = cameraImage.planes[2].bytes[uvIndex];

        int r = (yp + vp * 1436 / 1024 - 179).round().clamp(0, 255);
        int g = (yp - up * 46549 / 131072 + 44 - vp * 93604 / 131072 + 91).round().clamp(0, 255);
        int b = (yp + up * 1814 / 1024 - 227).round().clamp(0, 255);

        image.setPixel(x, y, img.getColor(r, g, b));
      }
    }

    return image;
  }

  // Convert BGRA8888 to Image
  static img.Image _convertBGRA8888ToImage(CameraImage cameraImage) {
    final int width = cameraImage.width;
    final int height = cameraImage.height;
    final Uint8List bytes = cameraImage.planes[0].bytes;
    
    final image = img.Image(width, height);

    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final int index = (y * width + x) * 4;
        final int b = bytes[index];
        final int g = bytes[index + 1];
        final int r = bytes[index + 2];
        final int a = bytes[index + 3];

        image.setPixel(x, y, img.getColor(r, g, b, a));
      }
    }
    
    return image;
  }

  // Resize image for processing
  static img.Image resizeImage(img.Image image, int targetWidth, int targetHeight) {
    return img.copyResize(
      image,
      width: targetWidth,
      height: targetHeight,
      interpolation: img.Interpolation.linear,
    );
  }

  // Convert image to bytes for ML processing
  static Uint8List imageToBytes(img.Image image) {
    return Uint8List.fromList(img.encodePng(image));
  }

  // Get image as RGB bytes for ML models
  static Float32List imageToFloat32List(img.Image image) {
    final Float32List result = Float32List(image.width * image.height * 3);
    int index = 0;
    
    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final pixel = image.getPixel(x, y);
        result[index++] = img.getRed(pixel) / 255.0;
        result[index++] = img.getGreen(pixel) / 255.0;
        result[index++] = img.getBlue(pixel) / 255.0;
      }
    }
    
    return result;
  }

  // Switch between front and back camera
  Future<bool> switchCamera() async {
    if (_cameras == null || _cameras!.length < 2) {
      return false;
    }

    try {
      await dispose();
      
      // Find the other camera
      CameraDescription newCamera = _cameras!.first;
      if (_controller?.description.lensDirection == CameraLensDirection.front) {
        // Switch to back camera
        for (final camera in _cameras!) {
          if (camera.lensDirection == CameraLensDirection.back) {
            newCamera = camera;
            break;
          }
        }
      } else {
        // Switch to front camera
        for (final camera in _cameras!) {
          if (camera.lensDirection == CameraLensDirection.front) {
            newCamera = camera;
            break;
          }
        }
      }

      _controller = CameraController(
        newCamera,
        ResolutionPreset.medium,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.yuv420,
      );

      await _controller!.initialize();
      _isInitialized = true;
      
      return true;
    } catch (e) {
      debugPrint('Error switching camera: $e');
      return false;
    }
  }

  // Set flash mode
  Future<void> setFlashMode(FlashMode flashMode) async {
    if (_controller != null && _isInitialized) {
      try {
        await _controller!.setFlashMode(flashMode);
      } catch (e) {
        debugPrint('Error setting flash mode: $e');
      }
    }
  }

  // Set zoom level
  Future<void> setZoomLevel(double zoomLevel) async {
    if (_controller != null && _isInitialized) {
      try {
        final double maxZoom = await _controller!.getMaxZoomLevel();
        final double minZoom = await _controller!.getMinZoomLevel();
        final double clampedZoom = zoomLevel.clamp(minZoom, maxZoom);
        await _controller!.setZoomLevel(clampedZoom);
      } catch (e) {
        debugPrint('Error setting zoom level: $e');
      }
    }
  }

  // Get camera preview size
  Size? getPreviewSize() {
    if (_controller != null && _isInitialized) {
      return _controller!.value.previewSize;
    }
    return null;
  }

  // Check if camera is available
  bool get isCameraAvailable => _cameras != null && _cameras!.isNotEmpty;

  // Check if front camera is available
  bool get isFrontCameraAvailable {
    if (_cameras == null) return false;
    return _cameras!.any((camera) => camera.lensDirection == CameraLensDirection.front);
  }

  // Check if back camera is available
  bool get isBackCameraAvailable {
    if (_cameras == null) return false;
    return _cameras!.any((camera) => camera.lensDirection == CameraLensDirection.back);
  }

  // Dispose camera resources
  Future<void> dispose() async {
    try {
      await stopImageStream();
      if (_controller != null) {
        await _controller!.dispose();
        _controller = null;
      }
      _isInitialized = false;
      _imageStreamController?.close();
      _imageStreamController = null;
      debugPrint('Camera disposed');
    } catch (e) {
      debugPrint('Error disposing camera: $e');
    }
  }
}
