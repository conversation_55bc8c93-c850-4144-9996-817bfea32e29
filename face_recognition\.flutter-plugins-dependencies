{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "camera_avfoundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\camera_avfoundation-0.9.21+2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\connectivity_plus-5.0.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage-9.2.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mlkit_barcode_scanning", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_barcode_scanning-0.10.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_commons", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_commons-0.6.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mlkit_digital_ink_recognition", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_digital_ink_recognition-0.10.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_entity_extraction", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_entity_extraction-0.11.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_face_detection", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_face_detection-0.9.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_face_mesh_detection", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_face_mesh_detection-0.0.2\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_image_labeling", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_image_labeling-0.10.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_language_id", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_language_id-0.9.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mlkit_object_detection", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_object_detection-0.11.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_pose_detection", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_pose_detection-0.10.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_selfie_segmentation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_selfie_segmentation-0.6.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_smart_reply", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_smart_reply-0.9.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mlkit_text_recognition", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_text_recognition-0.11.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_translation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_translation-0.9.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.2\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_apple", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_apple-9.4.7\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sqflite_darwin-2.4.2\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "tflite_flutter", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\tflite_flutter-0.9.5\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "camera_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\camera_android-0.9.8+3\\\\", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "connectivity_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\connectivity_plus-5.0.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_plugin_android_lifecycle", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_plugin_android_lifecycle-2.0.30\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage-9.2.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mlkit_barcode_scanning", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_barcode_scanning-0.10.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_commons", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_commons-0.6.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mlkit_digital_ink_recognition", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_digital_ink_recognition-0.10.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_entity_extraction", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_entity_extraction-0.11.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_face_detection", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_face_detection-0.9.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_face_mesh_detection", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_face_mesh_detection-0.0.2\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_image_labeling", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_image_labeling-0.10.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_language_id", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_language_id-0.9.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mlkit_object_detection", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_object_detection-0.11.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_pose_detection", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_pose_detection-0.10.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_selfie_segmentation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_selfie_segmentation-0.6.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_smart_reply", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_smart_reply-0.9.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mlkit_text_recognition", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_text_recognition-0.11.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "google_mlkit_translation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_translation-0.9.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "path_provider_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_android-2.2.18\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_android-12.1.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_android-2.4.12\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sqflite_android-2.4.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "tflite_flutter", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\tflite_flutter-0.9.5\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "connectivity_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\connectivity_plus-5.0.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage_macos-3.1.3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.2\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sqflite_darwin-2.4.2\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "connectivity_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\connectivity_plus-5.0.2\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage_linux-1.2.3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_linux-2.2.1\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_linux-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}, {"name": "tflite_flutter", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\tflite_flutter-0.9.5\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "windows": [{"name": "connectivity_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\connectivity_plus-5.0.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage_windows-3.1.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_windows-2.3.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_windows-0.2.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_windows-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}], "web": [{"name": "camera_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\camera_web-0.2.1+6\\\\", "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\connectivity_plus-5.0.2\\\\", "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage_web-1.2.1\\\\", "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_html", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_html-0.1.3+5\\\\", "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_web-2.4.3\\\\", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "camera", "dependencies": ["camera_android", "camera_avfoundation", "camera_web", "flutter_plugin_android_lifecycle"]}, {"name": "camera_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "camera_avfoundation", "dependencies": []}, {"name": "camera_web", "dependencies": []}, {"name": "connectivity_plus", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "flutter_secure_storage", "dependencies": ["flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_web", "flutter_secure_storage_windows"]}, {"name": "flutter_secure_storage_linux", "dependencies": []}, {"name": "flutter_secure_storage_macos", "dependencies": []}, {"name": "flutter_secure_storage_web", "dependencies": []}, {"name": "flutter_secure_storage_windows", "dependencies": ["path_provider"]}, {"name": "google_mlkit_barcode_scanning", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_commons", "dependencies": []}, {"name": "google_mlkit_digital_ink_recognition", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_entity_extraction", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_face_detection", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_face_mesh_detection", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_image_labeling", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_language_id", "dependencies": []}, {"name": "google_mlkit_object_detection", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_pose_detection", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_selfie_segmentation", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_smart_reply", "dependencies": []}, {"name": "google_mlkit_text_recognition", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_translation", "dependencies": ["google_mlkit_commons"]}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}, {"name": "tflite_flutter", "dependencies": []}], "date_created": "2025-08-30 10:30:24.714387", "version": "3.32.5", "swift_package_manager_enabled": {"ios": false, "macos": false}}