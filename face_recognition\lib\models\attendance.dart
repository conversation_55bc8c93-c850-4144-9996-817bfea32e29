enum AttendanceStatus {
  present,
  absent,
  late,
}

enum AttendanceMethod {
  faceRecognition,
  manual,
  qrCode,
}

class Attendance {
  final String id;
  final String studentId;
  final String studentName;
  final String className;
  final DateTime timestamp;
  final AttendanceStatus status;
  final AttendanceMethod method;
  final double? confidence; // Face recognition confidence score
  final String? location;
  final String? notes;
  final bool synced; // Whether this record has been synced to server
  final DateTime? syncedAt;

  Attendance({
    required this.id,
    required this.studentId,
    required this.studentName,
    required this.className,
    required this.timestamp,
    required this.status,
    required this.method,
    this.confidence,
    this.location,
    this.notes,
    this.synced = false,
    this.syncedAt,
  });

  // Convert Attendance to Map for database storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'studentId': studentId,
      'studentName': studentName,
      'className': className,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'status': status.name,
      'method': method.name,
      'confidence': confidence,
      'location': location,
      'notes': notes,
      'synced': synced ? 1 : 0,
      'syncedAt': syncedAt?.millisecondsSinceEpoch,
    };
  }

  // Create Attendance from Map (database)
  factory Attendance.fromMap(Map<String, dynamic> map) {
    return Attendance(
      id: map['id'] ?? '',
      studentId: map['studentId'] ?? '',
      studentName: map['studentName'] ?? '',
      className: map['className'] ?? '',
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp'] ?? 0),
      status: AttendanceStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => AttendanceStatus.present,
      ),
      method: AttendanceMethod.values.firstWhere(
        (e) => e.name == map['method'],
        orElse: () => AttendanceMethod.faceRecognition,
      ),
      confidence: map['confidence']?.toDouble(),
      location: map['location'],
      notes: map['notes'],
      synced: (map['synced'] ?? 0) == 1,
      syncedAt: map['syncedAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['syncedAt'])
          : null,
    );
  }

  // Convert Attendance to JSON for API
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'studentId': studentId,
      'studentName': studentName,
      'className': className,
      'timestamp': timestamp.toIso8601String(),
      'status': status.name,
      'method': method.name,
      'confidence': confidence,
      'location': location,
      'notes': notes,
      'synced': synced,
      'syncedAt': syncedAt?.toIso8601String(),
    };
  }

  // Create Attendance from JSON (API response)
  factory Attendance.fromJson(Map<String, dynamic> json) {
    return Attendance(
      id: json['id'] ?? '',
      studentId: json['studentId'] ?? '',
      studentName: json['studentName'] ?? '',
      className: json['className'] ?? '',
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      status: AttendanceStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => AttendanceStatus.present,
      ),
      method: AttendanceMethod.values.firstWhere(
        (e) => e.name == json['method'],
        orElse: () => AttendanceMethod.faceRecognition,
      ),
      confidence: json['confidence']?.toDouble(),
      location: json['location'],
      notes: json['notes'],
      synced: json['synced'] ?? false,
      syncedAt: json['syncedAt'] != null 
          ? DateTime.parse(json['syncedAt'])
          : null,
    );
  }

  // Copy with method for updating attendance data
  Attendance copyWith({
    String? id,
    String? studentId,
    String? studentName,
    String? className,
    DateTime? timestamp,
    AttendanceStatus? status,
    AttendanceMethod? method,
    double? confidence,
    String? location,
    String? notes,
    bool? synced,
    DateTime? syncedAt,
  }) {
    return Attendance(
      id: id ?? this.id,
      studentId: studentId ?? this.studentId,
      studentName: studentName ?? this.studentName,
      className: className ?? this.className,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
      method: method ?? this.method,
      confidence: confidence ?? this.confidence,
      location: location ?? this.location,
      notes: notes ?? this.notes,
      synced: synced ?? this.synced,
      syncedAt: syncedAt ?? this.syncedAt,
    );
  }

  // Helper methods
  String get statusDisplayName {
    switch (status) {
      case AttendanceStatus.present:
        return 'Present';
      case AttendanceStatus.absent:
        return 'Absent';
      case AttendanceStatus.late:
        return 'Late';
    }
  }

  String get methodDisplayName {
    switch (method) {
      case AttendanceMethod.faceRecognition:
        return 'Face Recognition';
      case AttendanceMethod.manual:
        return 'Manual';
      case AttendanceMethod.qrCode:
        return 'QR Code';
    }
  }

  String get formattedTimestamp {
    return '${timestamp.day}/${timestamp.month}/${timestamp.year} ${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';
  }

  @override
  String toString() {
    return 'Attendance{id: $id, studentName: $studentName, status: $status, timestamp: $timestamp}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Attendance && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
