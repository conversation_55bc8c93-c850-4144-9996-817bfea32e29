// Unit tests for the Face Recognition app models

import 'package:flutter_test/flutter_test.dart';
import 'package:face_recognition/models/face_embedding.dart';
import 'package:face_recognition/models/student.dart';

void main() {
  group('FaceEmbedding Model Tests', () {
    test('FaceEmbedding can be created with required parameters', () {
      final embedding = FaceEmbedding(
        id: 'test-id',
        studentId: 'student-123',
        embedding: [0.1, 0.2, 0.3, 0.4, 0.5],
        quality: 0.95,
        createdAt: DateTime.now(),
      );

      expect(embedding.id, 'test-id');
      expect(embedding.studentId, 'student-123');
      expect(embedding.embedding.length, 5);
      expect(embedding.quality, 0.95);
      expect(embedding.imagePath, isNull);
    });

    test('FaceEmbedding toMap and fromMap work correctly', () {
      final now = DateTime.now();
      final embedding = FaceEmbedding(
        id: 'test-id',
        studentId: 'student-123',
        embedding: [0.1, 0.2, 0.3],
        quality: 0.95,
        createdAt: now,
        imagePath: '/path/to/image.jpg',
      );

      final map = embedding.toMap();
      final reconstructed = FaceEmbedding.fromMap(map);

      expect(reconstructed.id, embedding.id);
      expect(reconstructed.studentId, embedding.studentId);
      expect(reconstructed.embedding, embedding.embedding);
      expect(reconstructed.quality, embedding.quality);
      expect(reconstructed.imagePath, embedding.imagePath);
    });

    test('FaceEmbedding toJson and fromJson work correctly', () {
      final now = DateTime.now();
      final embedding = FaceEmbedding(
        id: 'test-id',
        studentId: 'student-123',
        embedding: [0.1, 0.2, 0.3],
        quality: 0.95,
        createdAt: now,
      );

      final json = embedding.toJson();
      final reconstructed = FaceEmbedding.fromJson(json);

      expect(reconstructed.id, embedding.id);
      expect(reconstructed.studentId, embedding.studentId);
      expect(reconstructed.embedding, embedding.embedding);
      expect(reconstructed.quality, embedding.quality);
    });

    test('FaceEmbedding cosine similarity calculation works', () {
      final embedding1 = [1.0, 0.0, 0.0];
      final embedding2 = [0.0, 1.0, 0.0];
      final embedding3 = [1.0, 0.0, 0.0];

      final similarity1 = FaceEmbedding.cosineSimilarity(embedding1, embedding2);
      final similarity2 = FaceEmbedding.cosineSimilarity(embedding1, embedding3);

      expect(similarity1, closeTo(0.0, 0.001)); // Orthogonal vectors
      expect(similarity2, closeTo(1.0, 0.001)); // Identical vectors
    });

    test('FaceEmbedding euclidean distance calculation works', () {
      final embedding1 = [0.0, 0.0, 0.0];
      final embedding2 = [1.0, 1.0, 1.0];
      final embedding3 = [0.0, 0.0, 0.0];

      final distance1 = FaceEmbedding.euclideanDistance(embedding1, embedding2);
      final distance2 = FaceEmbedding.euclideanDistance(embedding1, embedding3);

      expect(distance1, closeTo(1.732, 0.001)); // sqrt(3)
      expect(distance2, closeTo(0.0, 0.001)); // Same vectors
    });
  });

  group('Student Model Tests', () {
    test('Student can be created with required parameters', () {
      final student = Student(
        id: 'student-123',
        name: 'John Doe',
        email: '<EMAIL>',
        className: 'CS101',
        faceEmbeddings: [[0.1, 0.2, 0.3]],
        createdAt: DateTime.now(),
      );

      expect(student.id, 'student-123');
      expect(student.name, 'John Doe');
      expect(student.email, '<EMAIL>');
      expect(student.className, 'CS101');
      expect(student.faceEmbeddings.length, 1);
      expect(student.photoPath, isNull);
      expect(student.updatedAt, isNull);
    });

    test('Student toMap and fromMap work correctly', () {
      final now = DateTime.now();
      final student = Student(
        id: 'student-123',
        name: 'John Doe',
        email: '<EMAIL>',
        className: 'CS101',
        photoPath: '/path/to/photo.jpg',
        faceEmbeddings: [[0.1, 0.2, 0.3], [0.4, 0.5, 0.6]],
        createdAt: now,
        updatedAt: now,
      );

      final map = student.toMap();
      final reconstructed = Student.fromMap(map);

      expect(reconstructed.id, student.id);
      expect(reconstructed.name, student.name);
      expect(reconstructed.email, student.email);
      expect(reconstructed.className, student.className);
      expect(reconstructed.photoPath, student.photoPath);
      expect(reconstructed.faceEmbeddings.length, 2);
      expect(reconstructed.faceEmbeddings[0], [0.1, 0.2, 0.3]);
      expect(reconstructed.faceEmbeddings[1], [0.4, 0.5, 0.6]);
    });

    test('Student toJson and fromJson work correctly', () {
      final now = DateTime.now();
      final student = Student(
        id: 'student-123',
        name: 'John Doe',
        email: '<EMAIL>',
        className: 'CS101',
        faceEmbeddings: [[0.1, 0.2, 0.3]],
        createdAt: now,
      );

      final json = student.toJson();
      final reconstructed = Student.fromJson(json);

      expect(reconstructed.id, student.id);
      expect(reconstructed.name, student.name);
      expect(reconstructed.email, student.email);
      expect(reconstructed.className, student.className);
      expect(reconstructed.faceEmbeddings.length, 1);
    });

    test('Student copyWith works correctly', () {
      final student = Student(
        id: 'student-123',
        name: 'John Doe',
        email: '<EMAIL>',
        className: 'CS101',
        faceEmbeddings: [[0.1, 0.2, 0.3]],
        createdAt: DateTime.now(),
      );

      final updated = student.copyWith(
        name: 'Jane Doe',
        email: '<EMAIL>',
      );

      expect(updated.id, student.id); // Unchanged
      expect(updated.name, 'Jane Doe'); // Changed
      expect(updated.email, '<EMAIL>'); // Changed
      expect(updated.className, student.className); // Unchanged
    });

    test('Student equality works correctly', () {
      final student1 = Student(
        id: 'student-123',
        name: 'John Doe',
        email: '<EMAIL>',
        className: 'CS101',
        faceEmbeddings: [[0.1, 0.2, 0.3]],
        createdAt: DateTime.now(),
      );

      final student2 = Student(
        id: 'student-123',
        name: 'Jane Doe', // Different name
        email: '<EMAIL>', // Different email
        className: 'CS102', // Different class
        faceEmbeddings: [[0.4, 0.5, 0.6]], // Different embeddings
        createdAt: DateTime.now(),
      );

      final student3 = Student(
        id: 'student-456', // Different ID
        name: 'John Doe',
        email: '<EMAIL>',
        className: 'CS101',
        faceEmbeddings: [[0.1, 0.2, 0.3]],
        createdAt: DateTime.now(),
      );

      expect(student1 == student2, isTrue); // Same ID
      expect(student1 == student3, isFalse); // Different ID
      expect(student1.hashCode, student2.hashCode); // Same hash for same ID
    });
  });
}
